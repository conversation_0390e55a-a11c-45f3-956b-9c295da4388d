概述：这是一个基于rbac系统的扩展系统，基于vue3+fastapi+mysql+redis的前后端分离系统。

开发环境是windows11，默认终端是powershell，开发过程中请生成powershell风格的语句和命令。
前端：vue3
后端：fastapi
开发数据库：mysql8.0.43(mysql://rbacpuls_devuser:m4itRTE7hVz4tSQLQLGJ@192.168.33.234:8043/rbacpuls_dev)
正式数据库：mysql8.0.43(mysql://rbacplus_produser:6YNoxnshxxoahgjdpzml@192.168.33.234:8043/rbacplus_prod)
开发redis：redis8.0.3(redis://:LxhgKEP5DJICDZNYNQLN@192.168.33.234:6803/2)
正式redis：redis8.0.3(redis://:LxhgKEP5DJICDZNYNQLN@192.168.33.234:6803/12)

项目整体要求
1、项目整体使用软删除功能；
2、表对象之间的关联关系，使用额外的关系表进行管理；
3、项目整体使用utf-8编码，编写测试文件时，也使用utf-8编码构建测试数据；
4、所有测试性质的代码都使用测试文件进行测试，不要直接输出测试代码到终端；
5、后端所有的功能模块都放在backend/apps文件夹下；
6、开发过程中生成的md文件存放在项目目录下的"开发文档"文件夹下；
7、开发过程中生成的测试python脚本和检查python脚本放在backend目录下的"测试脚本"文件夹下；
8、模型设计中，除了必要的逻辑字段外，对用户显示的字段不要使用唯一约束；
9、后端启动时校验SM2密钥对，如果失败则停止启动；

基础功能模块
1、登录注册模块：验证码开关，自助注册开关；前端向后端传递密码时，使用SM2算法加密传递；
2、用户中心模块：用户自身信息查看和修改，修改密码，头像上传修改；
3、首页模块：一个不需要任何权限即可查看的首页、一个不需要任何权限即可查看的关于页面；
4、用户管理模块：用户名最少5个字符，最多32个字符，用户名唯一，不许使用特殊字符；需要有用户来源字段(0为数据库直接添加，1为管理员添加，2为客户端添加，3为自助注册，4为未知来源)；用户密码最少8位，必须有大写字母、小写字母、数字;使用用户名作为盐，密码加盐后使用SM4算法加密存储；默认管理员：area0，默认密码：Ram10240；
5、角色管理模块：用模块户通过绑定角色来获得权限；默认包含超级管理员(包含所有权限)、系统管理员(用户的增删改查)、安全管理员(角色和权限的增删改查、启停用户)、审计管理员(查看系统的审计日志)、普通用户；
6、权限管理模块：单项权限的增删改查；
7、菜单管理模块：菜单项的增删改查，排序调整；
8、菜单管理模块：根据用户权限改变导航栏的内容；
9、组织管理模块：用于控制数据权限；
10、审计日志模块：访问审计和操作审计都需要有，分开展示；
11、统计分析模块：PV、UV；
12、码表(数据字典)管理模块；
13、页脚管理模块；
14、系统设置模块：系统名称、logo、favicon、版权信息、版本号、开发人员、开发时间、系统介绍、系统说明、系统使用协议、系统隐私协议、系统安全协议、系统更新日志、系统常见问题、系统帮助文档、系统在线备份、系统在线恢复；
15、定时任务管理模块；