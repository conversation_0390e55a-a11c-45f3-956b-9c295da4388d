概述：这是一个基于rbac系统的扩展系统，基于vue3+fastapi+mysql+redis的前后端分离系统。

开发环境是windows11，默认终端是powershell，开发过程中请生成powershell风格的语句和命令。
前端：vue3
后端：fastapi
开发数据库：mysql8.0.43(mysql://rbacpuls_devuser:m4itRTE7hVz4tSQLQLGJ@192.168.33.234:8043/rbacpuls_dev)
正式数据库：mysql8.0.43(mysql://rbacplus_produser:6YNoxnshxxoahgjdpzml@192.168.33.234:8043/rbacplus_prod)
开发redis：redis8.0.3(redis://:LxhgKEP5DJICDZNYNQLN@192.168.33.234:6803/2)
正式redis：redis8.0.3(redis://:LxhgKEP5DJICDZNYNQLN@192.168.33.234:6803/12)

项目整体要求
1、项目整体使用软删除功能；
2、表对象之间的关联关系，使用额外的关系表进行管理；
3、项目整体使用utf-8编码，编写测试文件时，也使用utf-8编码构建测试数据；
4、所有测试性质的代码都使用测试文件进行测试，不要直接输出测试代码到终端；
5、前端项目放在frontend文件夹，后端项目放在backend文件夹；
6、后端所有的功能模块都放在backend/apps文件夹下；
7、测试环境，前端使用5179端口，后端使用8009端口；
8、开发过程中生成的md文件存放在项目目录下的"开发文档"文件夹下；
9、开发过程中生成的测试python脚本和检查python脚本放在backend目录下的"测试脚本"文件夹下；
10、模型设计中，除了必要的逻辑字段外，对用户显示的字段不要使用唯一约束；

基础功能模块
1、用户管理：需要有用户来源字段(0为数据库直接添加，1为管理员添加，2为客户端添加，3为自助注册，4为未知来源)；用户密码最少8位，必须有大写字母、小写字母、数字;使用用户名作为盐，密码加盐后使用SM4算法加密存储；前端向后端传递密码时，使用SM2算法加密传递。默认管理员：area0，默认密码：Ram10240；
2、角色管理：用户通过绑定角色来获得权限；默认包含超级管理员(包含所有权限)、系统管理员(用户的增删改查)、安全管理员(角色和权限的增删改查、启停用户)、审计管理员(查看系统的审计日志)；
3、权限管理：单项权限的增删改查；
4、菜单管理：菜单项的增删改查，排序调整；
5、动态菜单：根据用户权限改变导航栏的内容；
6、组织管理：用于控制数据权限；
7、审计日志：访问审计和操作审计都需要有，分开展示；
8、PV、UV统计；
9、后端配置文件中添加自助注册开关，自助注册为true则允许用户自主注册，用户模型要添加用户来源字段，用来区分用户是自助注册、管理员创建或者其他情况；
10、后端配置文件中添加图形验证码开关，图形验证码为true则用户登录时显示图形验证码；