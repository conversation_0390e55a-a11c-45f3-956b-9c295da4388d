{"version": 3, "file": "useKeydown.mjs", "sources": ["../../../../../../../packages/components/tree/src/model/useKeydown.ts"], "sourcesContent": ["import { onMounted, onUpdated } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type TreeStore from './tree-store'\nimport type { Ref } from 'vue'\nimport type { Nullable } from '@element-plus/utils'\n\ninterface UseKeydownOption {\n  el$: Ref<HTMLElement | null>\n}\nexport function useKeydown({ el$ }: UseKeydownOption, store: Ref<TreeStore>) {\n  const ns = useNamespace('tree')\n\n  onMounted(() => {\n    initTabIndex()\n  })\n\n  onUpdated(() => {\n    const checkboxItems = Array.from(\n      el$.value!.querySelectorAll('input[type=checkbox]')\n    )\n    checkboxItems.forEach((checkbox) => {\n      checkbox.setAttribute('tabindex', '-1')\n    })\n  })\n\n  function canNodeFocus(treeItems: HTMLElement[], nextIndex: number): boolean {\n    const currentNode = store.value.getNode(treeItems[nextIndex].dataset.key!)\n    return (\n      currentNode.canFocus &&\n      currentNode.visible &&\n      (currentNode.parent?.expanded || currentNode.parent?.level === 0)\n    )\n  }\n\n  const handleKeydown = (ev: KeyboardEvent): void => {\n    const currentItem = ev.target as HTMLDivElement\n    if (!currentItem.className.includes(ns.b('node'))) return\n    const code = ev.code\n    const treeItems: HTMLElement[] = Array.from(\n      el$.value!.querySelectorAll(`.${ns.is('focusable')}[role=treeitem]`)\n    )\n    const currentIndex = treeItems.indexOf(currentItem)\n    let nextIndex\n    if ([EVENT_CODE.up, EVENT_CODE.down].includes(code)) {\n      ev.preventDefault()\n      if (code === EVENT_CODE.up) {\n        nextIndex =\n          currentIndex === -1\n            ? 0\n            : currentIndex !== 0\n            ? currentIndex - 1\n            : treeItems.length - 1\n        const startIndex = nextIndex\n        while (true) {\n          if (canNodeFocus(treeItems, nextIndex)) {\n            break\n          }\n\n          nextIndex--\n          if (nextIndex === startIndex) {\n            nextIndex = -1\n            break\n          }\n          if (nextIndex < 0) {\n            nextIndex = treeItems.length - 1\n          }\n        }\n      } else {\n        nextIndex =\n          currentIndex === -1\n            ? 0\n            : currentIndex < treeItems.length - 1\n            ? currentIndex + 1\n            : 0\n        const startIndex = nextIndex\n        while (true) {\n          if (canNodeFocus(treeItems, nextIndex)) {\n            break\n          }\n\n          nextIndex++\n          if (nextIndex === startIndex) {\n            nextIndex = -1\n            break\n          }\n          if (nextIndex >= treeItems.length) {\n            nextIndex = 0\n          }\n        }\n      }\n      nextIndex !== -1 && treeItems[nextIndex].focus()\n    }\n    if ([EVENT_CODE.left, EVENT_CODE.right].includes(code)) {\n      ev.preventDefault()\n      currentItem.click()\n    }\n    const hasInput = currentItem.querySelector(\n      '[type=\"checkbox\"]'\n    ) as Nullable<HTMLInputElement>\n    if (\n      [EVENT_CODE.enter, EVENT_CODE.numpadEnter, EVENT_CODE.space].includes(\n        code\n      ) &&\n      hasInput\n    ) {\n      ev.preventDefault()\n      hasInput.click()\n    }\n  }\n\n  useEventListener(el$, 'keydown', handleKeydown)\n\n  const initTabIndex = (): void => {\n    if (!el$.value) return\n    const treeItems = Array.from(\n      el$.value.querySelectorAll(`.${ns.is('focusable')}[role=treeitem]`)\n    )\n    const checkboxItems = Array.from(\n      el$.value.querySelectorAll('input[type=checkbox]')\n    )\n    checkboxItems.forEach((checkbox) => {\n      checkbox.setAttribute('tabindex', '-1')\n    })\n    const checkedItem = el$.value.querySelectorAll(\n      `.${ns.is('checked')}[role=treeitem]`\n    )\n    if (checkedItem.length) {\n      checkedItem[0].setAttribute('tabindex', '0')\n      return\n    }\n    treeItems[0]?.setAttribute('tabindex', '0')\n  }\n}\n"], "names": [], "mappings": ";;;;;AAIO,SAAS,UAAU,CAAC,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE;AAC3C,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;AAClC,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,YAAY,EAAE,CAAC;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC,CAAC;AACzF,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK;AACxC,MAAM,QAAQ,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAC9C,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,YAAY,CAAC,SAAS,EAAE,SAAS,EAAE;AAC9C,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9E,IAAI,OAAO,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,OAAO,KAAK,CAAC,CAAC,EAAE,GAAG,WAAW,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE,GAAG,WAAW,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC;AACxL,GAAG;AACH,EAAE,MAAM,aAAa,GAAG,CAAC,EAAE,KAAK;AAChC,IAAI,MAAM,WAAW,GAAG,EAAE,CAAC,MAAM,CAAC;AAClC,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACrD,MAAM,OAAO;AACb,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACzB,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;AACtG,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACxD,IAAI,IAAI,SAAS,CAAC;AAClB,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACzD,MAAM,EAAE,CAAC,cAAc,EAAE,CAAC;AAC1B,MAAM,IAAI,IAAI,KAAK,UAAU,CAAC,EAAE,EAAE;AAClC,QAAQ,SAAS,GAAG,YAAY,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,KAAK,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3G,QAAQ,MAAM,UAAU,GAAG,SAAS,CAAC;AACrC,QAAQ,OAAO,IAAI,EAAE;AACrB,UAAU,IAAI,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;AAClD,YAAY,MAAM;AAClB,WAAW;AACX,UAAU,SAAS,EAAE,CAAC;AACtB,UAAU,IAAI,SAAS,KAAK,UAAU,EAAE;AACxC,YAAY,SAAS,GAAG,CAAC,CAAC,CAAC;AAC3B,YAAY,MAAM;AAClB,WAAW;AACX,UAAU,IAAI,SAAS,GAAG,CAAC,EAAE;AAC7B,YAAY,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAC7C,WAAW;AACX,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,SAAS,GAAG,YAAY,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;AACzG,QAAQ,MAAM,UAAU,GAAG,SAAS,CAAC;AACrC,QAAQ,OAAO,IAAI,EAAE;AACrB,UAAU,IAAI,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;AAClD,YAAY,MAAM;AAClB,WAAW;AACX,UAAU,SAAS,EAAE,CAAC;AACtB,UAAU,IAAI,SAAS,KAAK,UAAU,EAAE;AACxC,YAAY,SAAS,GAAG,CAAC,CAAC,CAAC;AAC3B,YAAY,MAAM;AAClB,WAAW;AACX,UAAU,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,EAAE;AAC7C,YAAY,SAAS,GAAG,CAAC,CAAC;AAC1B,WAAW;AACX,SAAS;AACT,OAAO;AACP,MAAM,SAAS,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC;AACvD,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC5D,MAAM,EAAE,CAAC,cAAc,EAAE,CAAC;AAC1B,MAAM,WAAW,CAAC,KAAK,EAAE,CAAC;AAC1B,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,WAAW,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,EAAE;AACjG,MAAM,EAAE,CAAC,cAAc,EAAE,CAAC;AAC1B,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC;AACvB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,gBAAgB,CAAC,GAAG,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;AAClD,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK;AAClB,MAAM,OAAO;AACb,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;AACtG,IAAI,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC,CAAC;AACzF,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK;AACxC,MAAM,QAAQ,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAC9C,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;AAC1F,IAAI,IAAI,WAAW,CAAC,MAAM,EAAE;AAC5B,MAAM,WAAW,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACnD,MAAM,OAAO;AACb,KAAK;AACL,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AAC5E,GAAG,CAAC;AACJ;;;;"}