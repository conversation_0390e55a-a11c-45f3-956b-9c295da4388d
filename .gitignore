# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.cache
nosetests.xml
coverage.xml

# Translations
*.mo
*.pot

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Sphinx documentation
docs/_build/

# Editor directories and files
.augment-guidelines
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# augment
memory/
**/node_modules