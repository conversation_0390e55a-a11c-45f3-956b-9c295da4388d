{"version": 3, "file": "scrollbar2.mjs", "sources": ["../../../../../../packages/components/scrollbar/src/scrollbar.vue"], "sourcesContent": ["<template>\n  <div ref=\"scrollbarRef\" :class=\"ns.b()\">\n    <div\n      ref=\"wrapRef\"\n      :class=\"wrapKls\"\n      :style=\"wrapStyle\"\n      :tabindex=\"tabindex\"\n      @scroll=\"handleScroll\"\n    >\n      <component\n        :is=\"tag\"\n        :id=\"id\"\n        ref=\"resizeRef\"\n        :class=\"resizeKls\"\n        :style=\"viewStyle\"\n        :role=\"role\"\n        :aria-label=\"ariaLabel\"\n        :aria-orientation=\"ariaOrientation\"\n      >\n        <slot />\n      </component>\n    </div>\n    <template v-if=\"!native\">\n      <bar ref=\"barRef\" :always=\"always\" :min-size=\"minSize\" />\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onActivated,\n  onMounted,\n  onUpdated,\n  provide,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { useEventListener, useResizeObserver } from '@vueuse/core'\nimport { addUnit, debugWarn, isNumber, isObject } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport Bar from './bar.vue'\nimport { scrollbarContextKey } from './constants'\nimport { scrollbarEmits, scrollbarProps } from './scrollbar'\n\nimport type { ScrollbarDirection } from './scrollbar'\nimport type { BarInstance } from './bar'\nimport type { CSSProperties, StyleValue } from 'vue'\n\nconst COMPONENT_NAME = 'ElScrollbar'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(scrollbarProps)\nconst emit = defineEmits(scrollbarEmits)\n\nconst ns = useNamespace('scrollbar')\n\nlet stopResizeObserver: (() => void) | undefined = undefined\nlet stopResizeListener: (() => void) | undefined = undefined\nlet wrapScrollTop = 0\nlet wrapScrollLeft = 0\nlet direction = '' as ScrollbarDirection\nconst distanceScrollState = {\n  bottom: false,\n  top: false,\n  right: false,\n  left: false,\n}\n\nconst scrollbarRef = ref<HTMLDivElement>()\nconst wrapRef = ref<HTMLDivElement>()\nconst resizeRef = ref<HTMLElement>()\nconst barRef = ref<BarInstance>()\n\nconst wrapStyle = computed<StyleValue>(() => {\n  const style: CSSProperties = {}\n  if (props.height) style.height = addUnit(props.height)\n  if (props.maxHeight) style.maxHeight = addUnit(props.maxHeight)\n  return [props.wrapStyle, style]\n})\n\nconst wrapKls = computed(() => {\n  return [\n    props.wrapClass,\n    ns.e('wrap'),\n    { [ns.em('wrap', 'hidden-default')]: !props.native },\n  ]\n})\n\nconst resizeKls = computed(() => {\n  return [ns.e('view'), props.viewClass]\n})\n\nconst shouldSkipDirection = (direction: ScrollbarDirection) => {\n  return distanceScrollState[direction] ?? false\n}\n\nconst DIRECTION_PAIRS: Record<ScrollbarDirection, ScrollbarDirection> = {\n  top: 'bottom',\n  bottom: 'top',\n  left: 'right',\n  right: 'left',\n}\nconst updateTriggerStatus = (arrivedStates: Record<string, boolean>) => {\n  const oppositeDirection = DIRECTION_PAIRS[direction]\n  if (!oppositeDirection) return\n\n  const arrived = arrivedStates[direction]\n  const oppositeArrived = arrivedStates[oppositeDirection]\n\n  if (arrived && !distanceScrollState[direction]) {\n    distanceScrollState[direction] = true\n  }\n\n  if (!oppositeArrived && distanceScrollState[oppositeDirection]) {\n    distanceScrollState[oppositeDirection] = false\n  }\n}\n\nconst handleScroll = () => {\n  if (wrapRef.value) {\n    barRef.value?.handleScroll(wrapRef.value)\n    const prevTop = wrapScrollTop\n    const prevLeft = wrapScrollLeft\n    wrapScrollTop = wrapRef.value.scrollTop\n    wrapScrollLeft = wrapRef.value.scrollLeft\n\n    const arrivedStates = {\n      bottom:\n        wrapScrollTop + wrapRef.value.clientHeight >=\n        wrapRef.value.scrollHeight - props.distance,\n      top: wrapScrollTop <= props.distance && prevTop !== 0,\n      right:\n        wrapScrollLeft + wrapRef.value.clientWidth >=\n          wrapRef.value.scrollWidth - props.distance &&\n        prevLeft !== wrapScrollLeft,\n      left: wrapScrollLeft <= props.distance && prevLeft !== 0,\n    }\n\n    emit('scroll', {\n      scrollTop: wrapScrollTop,\n      scrollLeft: wrapScrollLeft,\n    })\n\n    if (prevTop !== wrapScrollTop) {\n      direction = wrapScrollTop > prevTop ? 'bottom' : 'top'\n    }\n    if (prevLeft !== wrapScrollLeft) {\n      direction = wrapScrollLeft > prevLeft ? 'right' : 'left'\n    }\n    if (props.distance > 0) {\n      if (shouldSkipDirection(direction)) {\n        return\n      }\n      updateTriggerStatus(arrivedStates)\n    }\n    if (arrivedStates[direction]) emit('end-reached', direction)\n  }\n}\n\nfunction scrollTo(xCord: number, yCord?: number): void\nfunction scrollTo(options: ScrollToOptions): void\nfunction scrollTo(arg1: unknown, arg2?: number) {\n  if (isObject(arg1)) {\n    wrapRef.value!.scrollTo(arg1)\n  } else if (isNumber(arg1) && isNumber(arg2)) {\n    wrapRef.value!.scrollTo(arg1, arg2)\n  }\n}\n\nconst setScrollTop = (value: number) => {\n  if (!isNumber(value)) {\n    debugWarn(COMPONENT_NAME, 'value must be a number')\n    return\n  }\n  wrapRef.value!.scrollTop = value\n}\n\nconst setScrollLeft = (value: number) => {\n  if (!isNumber(value)) {\n    debugWarn(COMPONENT_NAME, 'value must be a number')\n    return\n  }\n  wrapRef.value!.scrollLeft = value\n}\n\nconst update = () => {\n  barRef.value?.update()\n  distanceScrollState[direction] = false\n}\n\nwatch(\n  () => props.noresize,\n  (noresize) => {\n    if (noresize) {\n      stopResizeObserver?.()\n      stopResizeListener?.()\n    } else {\n      ;({ stop: stopResizeObserver } = useResizeObserver(resizeRef, update))\n      stopResizeListener = useEventListener('resize', update)\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => [props.maxHeight, props.height],\n  () => {\n    if (!props.native)\n      nextTick(() => {\n        update()\n        if (wrapRef.value) {\n          barRef.value?.handleScroll(wrapRef.value)\n        }\n      })\n  }\n)\n\nprovide(\n  scrollbarContextKey,\n  reactive({\n    scrollbarElement: scrollbarRef,\n    wrapElement: wrapRef,\n  })\n)\n\nonActivated(() => {\n  if (wrapRef.value) {\n    wrapRef.value.scrollTop = wrapScrollTop\n    wrapRef.value.scrollLeft = wrapScrollLeft\n  }\n})\n\nonMounted(() => {\n  if (!props.native)\n    nextTick(() => {\n      update()\n    })\n})\nonUpdated(() => update())\n\ndefineExpose({\n  /** @description scrollbar wrap ref */\n  wrapRef,\n  /** @description update scrollbar state manually */\n  update,\n  /** @description scrolls to a particular set of coordinates */\n  scrollTo,\n  /** @description set distance to scroll top */\n  setScrollTop,\n  /** @description set distance to scroll left */\n  setScrollLeft,\n  /** @description handle scroll event */\n  handleScroll,\n})\n</script>\n"], "names": ["direction", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_createElementVNode"], "mappings": ";;;;;;;;;;;;mCAqDc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAK,aAAa,WAAW,CAAA,CAAA;AAEnC,IAAA,IAAI,kBAA+C,GAAA,KAAA,CAAA,CAAA;AACnD,IAAA,IAAI,kBAA+C,GAAA,KAAA,CAAA,CAAA;AACnD,IAAA,IAAI,aAAgB,GAAA,CAAA,CAAA;AACpB,IAAA,IAAI,cAAiB,GAAA,CAAA,CAAA;AACrB,IAAA,IAAI,SAAY,GAAA,EAAA,CAAA;AAChB,IAAA,MAAM,mBAAsB,GAAA;AAAA,MAC1B,MAAQ,EAAA,KAAA;AAAA,MACR,GAAK,EAAA,KAAA;AAAA,MACL,KAAO,EAAA,KAAA;AAAA,MACP,IAAM,EAAA,KAAA;AAAA,KACR,CAAA;AAEA,IAAA,MAAM,eAAe,GAAoB,EAAA,CAAA;AACzC,IAAA,MAAM,UAAU,GAAoB,EAAA,CAAA;AACpC,IAAA,MAAM,YAAY,GAAiB,EAAA,CAAA;AACnC,IAAA,MAAM,SAAS,GAAiB,EAAA,CAAA;AAEhC,IAAM,MAAA,SAAA,GAAY,SAAqB,MAAM;AAC3C,MAAA,MAAM,QAAuB,EAAC,CAAA;AAC9B,MAAA,IAAI,MAAM,MAAQ;AAClB,QAAA,YAAqB,GAAA,OAAA,CAAA,KAAkB,CAAA,MAAA,CAAA,CAAA;AACvC,MAAO,IAAA,KAAO,CAAA,SAAA;AAAgB,QAC/B,KAAA,CAAA,SAAA,GAAA,OAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAED,MAAM,OAAA,CAAA,KAAA,CAAU,SAAS,EAAM,KAAA,CAAA,CAAA;AAC7B,KAAO,CAAA,CAAA;AAAA,IAAA,MACC,OAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACN,OAAK;AAAM,QACX,KAAG,CAAG,SAAG;AAA0C,QACrD,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,QACD,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,EAAA;AAED,OAAM,CAAA;AACJ,KAAA,CAAA,CAAA;AAAqC,IACvC,MAAC,SAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,OAAA,CAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA,KAAuBA,CAAkC,SAAA,CAAA,CAAA;AAC7D,KAAO,CAAA,CAAA;AAAkC,IAC3C,MAAA,mBAAA,GAAA,CAAA,UAAA,KAAA;AAEA,MAAA,IAAM,EAAkE,CAAA;AAAA,MACtE,OAAK,CAAA,EAAA,GAAA,mBAAA,CAAA,UAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA;AAAA,KAAA,CACL;AAAQ,IAAA,MACF,eAAA,GAAA;AAAA,MACN,GAAO,EAAA,QAAA;AAAA,MACT,MAAA,EAAA,KAAA;AACA,MAAM,IAAA,EAAA,OAAA;AACJ,MAAM,KAAA,EAAA,MAAA;AACN,KAAA,CAAA;AAEA,IAAM,MAAA,oCAAiC,KAAA;AACvC,MAAM,MAAA,iBAAA,kBAAiD,CAAA,SAAA,CAAA,CAAA;AAEvD,MAAA,IAAI,CAAW,iBAAqB;AAClC,QAAA,OAAA;AAAiC,MACnC,MAAA,OAAA,GAAA,aAAA,CAAA,SAAA,CAAA,CAAA;AAEA,MAAA,MAAK,eAAA,GAAmB,aAAoB,CAAA,iBAAA,CAAA,CAAA;AAC1C,MAAA,IAAA,OAAA,IAAA,CAAA,6BAAyC,CAAA,EAAA;AAAA,QAC3C,mBAAA,CAAA,SAAA,CAAA,GAAA,IAAA,CAAA;AAAA,OACF;AAEA,MAAA,IAAM,gBAAqB,IAAA,mBAAA,CAAA,iBAAA,CAAA,EAAA;AACzB,QAAA,mBAAmB,CAAA,iBAAA,CAAA,GAAA,KAAA,CAAA;AACjB,OAAO;AACP,KAAA,CAAA;AACA,IAAA,MAAA,YAAiB,GAAA,MAAA;AACjB,MAAA,IAAA,EAAA,CAAA;AACA,MAAA,IAAA,OAAA,CAAA,KAAA,EAAiB;AAEjB,QAAA,CAAA,EAAA,GAAM,MAAgB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACpB,MAAA,uBAC0B,CAAA;AACW,QAAA,MAChC,QAAA,GAAA,cAAuB,CAAA;AAAwB,QACpD,aACmB,GAAA,OAAA,CAAA,KAAA,CAAA,SAAc,CAAA;AAElB,QAAA,cACT,GAAA,OAAA,CAAA,KAAwB,CAAA,UAAA,CAAA;AAAyB,QACzD,MAAA,aAAA,GAAA;AAEA,UAAA,MAAe,EAAA,aAAA,GAAA,OAAA,CAAA,KAAA,CAAA,YAAA,IAAA,OAAA,CAAA,KAAA,CAAA,YAAA,GAAA,KAAA,CAAA,QAAA;AAAA,UACb,GAAW,EAAA,aAAA,IAAA,KAAA,CAAA,QAAA,IAAA,OAAA,KAAA,CAAA;AAAA,UACX,KAAY,EAAA,cAAA,GAAA,OAAA,CAAA,KAAA,CAAA,WAAA,IAAA,OAAA,CAAA,KAAA,CAAA,WAAA,GAAA,KAAA,CAAA,QAAA,IAAA,QAAA,KAAA,cAAA;AAAA,UACb,IAAA,EAAA,cAAA,IAAA,KAAA,CAAA,QAAA,IAAA,QAAA,KAAA,CAAA;AAED,SAAA,CAAA;AACE,QAAY,IAAA,CAAA,QAAA,EAAA;AAAqC,UACnD,SAAA,EAAA,aAAA;AACA,UAAA,0BAAiC;AAC/B,SAAY,CAAA,CAAA;AAAsC,QACpD,IAAA,OAAA,KAAA,aAAA,EAAA;AACA,UAAI,yBAAoB,GAAA,OAAA,GAAA,QAAA,GAAA,KAAA,CAAA;AACtB,SAAI;AACF,QAAA,IAAA,QAAA,KAAA,cAAA,EAAA;AAAA,UACF,SAAA,GAAA,cAAA,GAAA,QAAA,GAAA,OAAA,GAAA,MAAA,CAAA;AACA,SAAA;AAAiC,QACnC,IAAA,KAAA,CAAA,QAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAkB,mBAAA,CAAA,SAAiB;AAAwB,YAC7D,OAAA;AAAA,WACF;AAIA,UAAS,mBAAuC,CAAA,aAAA,CAAA,CAAA;AAC9C,SAAI;AACF,QAAQ,IAAA,uBAAoB,CAAA;AAAA,4BACV,EAAI,SAAK,CAAA,CAAA;AAC3B,OAAQ;AAA0B,KACpC,CAAA;AAAA,IACF,SAAA,QAAA,CAAA,IAAA,EAAA,IAAA,EAAA;AAEA,MAAM,IAAA,QAAA,CAAA,IAAA,CAAA,EAAgB;AACpB,QAAI,OAAU,CAAA,KAAA,CAAA,QAAQ,CAAA,IAAA,CAAA,CAAA;AACpB,OAAA,MAAA,IAAA,kBAAkD,QAAA,CAAA,IAAA,CAAA,EAAA;AAClD,QAAA,OAAA,CAAA,KAAA,CAAA,QAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAAA,OACF;AACA,KAAA;AAA2B,IAC7B,MAAA,YAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,IAAA,CAAA,QAAA,CAAA,KAAA,CAAgB,EAAmB;AAErC,QAAA,OAAA;AACA,OAAA;AAAA,MACF,OAAA,CAAA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA;AACA,KAAA,CAAA;AAA4B,IAC9B,MAAA,aAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAA,IAAM,SAAS,CAAM,KAAA,CAAA,EAAA;AAEnB,QAAA,OAAA;AAAiC,OACnC;AAEA,MAAA,OAAA,CAAA,KAAA,CAAA,UAAA,GAAA,KAAA,CAAA;AAAA,KAAA,CACE;AAAY,IAAA,MACE,MAAA,GAAA,MAAA;AACZ,MAAA,IAAA,EAAI,CAAU;AACZ,MAAqB,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AACrB,MAAqB,mBAAA,CAAA,SAAA,CAAA,GAAA,KAAA,CAAA;AAAA,KAAA,CAAA;AAErB,IAAA,KAAA,CAAA,MAAA,KAAA,CAAA,QAAA,EAAA,CAAA,QAAA,KAAA;AAAC,MAAA,IAAA,QAAS,EAAA;AACV,QAAqB,kBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,kBAAiC,EAAA,CAAA;AAAA,QACxD,kBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,kBAAA,EAAA,CAAA;AAAA,OACF,MAAA;AACkB,QACpB,CAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,GAAA,iBAAA,CAAA,SAAA,EAAA,MAAA,CAAA,EAAA;AAEA,QAAA,kBAAA,GAAA,gBAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;AAAA,OACQ;AAA8B,KAAA,EAC9B,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AACJ,IAAA,KAAA,CAAA,MAAW,CAAA,KAAA,CAAA,SAAA,EAAA,KAAA,CAAA,MAAA,CAAA,EAAA,MAAA;AACT,MAAA,IAAA,CAAA,KAAA,CAAA,MAAe;AACb,QAAO,QAAA,CAAA,MAAA;AACP,UAAA,IAAA,EAAI;AACF,UAAO,MAAA,EAAA,CAAA;AAAiC,UAC1C,IAAA,OAAA,CAAA,KAAA,EAAA;AAAA,YACD,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAAA,WACL;AAAA,SACF,CAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IACE,OAAA,CAAA,mBAAA,EAAA,QAAA,CAAA;AAAA,MACA,gBAAS,EAAA,YAAA;AAAA,MAAA,WACW,EAAA,OAAA;AAAA,KAAA,CAAA,CAAA,CAClB;AAAa,IAAA,WACd,CAAA,MAAA;AAAA,MACH,IAAA,OAAA,CAAA,KAAA,EAAA;AAEA,QAAA,OAAA,CAAY,KAAM,CAAA,SAAA,GAAA,aAAA,CAAA;AAChB,QAAA,aAAmB,CAAA,UAAA,GAAA,cAAA,CAAA;AACjB,OAAA;AACA,KAAA,CAAA,CAAA;AAA2B,IAC7B,SAAA,CAAA,MAAA;AAAA,MACD,IAAA,CAAA,KAAA,CAAA,MAAA;AAED,QAAA,QAAgB,CAAA,MAAA;AACd,UAAI,MAAO,EAAA,CAAA;AACT,SAAA,CAAA,CAAA;AACE,KAAO,CAAA,CAAA;AAAA,IAAA,SACR,CAAA,MAAA,MAAA,EAAA,CAAA,CAAA;AAAA,IACL,MAAC,CAAA;AACD,MAAU,OAAA;AAEV,MAAa,MAAA;AAAA,MAAA,QAAA;AAAA,MAEX,YAAA;AAAA,MAAA,aAAA;AAAA,MAEA,YAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAEA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,QAEA,OAAA,EAAA,cAAA;AAAA,QAAA,GAAA,EAAA,YAAA;AAAA,QAEA,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAEAC,kBAAA,CAAA,KAAA,EAAA;AAAA,UACD,OAAA,EAAA,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}