import type Steps from './steps.vue';
import type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue';
export declare const stepsProps: {
    readonly space: import("element-plus/es/utils").EpPropFinalized<readonly [NumberConstructor, StringConstructor], unknown, unknown, "", boolean>;
    readonly active: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly direction: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "horizontal" | "vertical", unknown, "horizontal", boolean>;
    readonly alignCenter: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly simple: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly finishStatus: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "wait" | "error" | "finish" | "success" | "process", unknown, "finish", boolean>;
    readonly processStatus: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "wait" | "error" | "finish" | "success" | "process", unknown, "process", boolean>;
};
export type StepsProps = ExtractPropTypes<typeof stepsProps>;
export type StepsPropsPublic = __ExtractPublicPropTypes<typeof stepsProps>;
export declare const stepsEmits: {
    change: (newVal: number, oldVal: number) => boolean;
};
export type StepsEmits = typeof stepsEmits;
export type StepsInstance = InstanceType<typeof Steps> & unknown;
