"""
Redis连接和缓存管理
"""

import aioredis
from typing import Optional, Any
from loguru import logger
import json
from datetime import timed<PERSON><PERSON>

from .config import get_redis_url


class RedisManager:
    """Redis管理器"""
    
    def __init__(self):
        self.redis: Optional[aioredis.Redis] = None
    
    async def connect(self):
        """连接Redis"""
        try:
            self.redis = aioredis.from_url(
                get_redis_url(),
                encoding="utf-8",
                decode_responses=True,
                max_connections=20
            )
            # 测试连接
            await self.redis.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            raise
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis:
            await self.redis.close()
            logger.info("Redis连接已关闭")
    
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置缓存"""
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)
            
            if expire:
                await self.redis.setex(key, expire, value)
            else:
                await self.redis.set(key, value)
            return True
        except Exception as e:
            logger.error(f"Redis设置失败: {e}")
            return False
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            value = await self.redis.get(key)
            if value is None:
                return None
            
            # 尝试解析JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        except Exception as e:
            logger.error(f"Redis获取失败: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            result = await self.redis.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Redis删除失败: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            return await self.redis.exists(key) > 0
        except Exception as e:
            logger.error(f"Redis检查存在失败: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置过期时间"""
        try:
            return await self.redis.expire(key, seconds)
        except Exception as e:
            logger.error(f"Redis设置过期时间失败: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """获取剩余过期时间"""
        try:
            return await self.redis.ttl(key)
        except Exception as e:
            logger.error(f"Redis获取TTL失败: {e}")
            return -1
    
    async def keys(self, pattern: str = "*") -> list:
        """获取匹配的键列表"""
        try:
            return await self.redis.keys(pattern)
        except Exception as e:
            logger.error(f"Redis获取键列表失败: {e}")
            return []
    
    async def flushdb(self) -> bool:
        """清空当前数据库"""
        try:
            await self.redis.flushdb()
            logger.info("Redis数据库已清空")
            return True
        except Exception as e:
            logger.error(f"Redis清空数据库失败: {e}")
            return False


# 全局Redis管理器实例
redis_manager = RedisManager()


async def get_redis() -> RedisManager:
    """获取Redis管理器"""
    return redis_manager


async def init_redis():
    """初始化Redis连接"""
    await redis_manager.connect()


async def close_redis():
    """关闭Redis连接"""
    await redis_manager.disconnect()


# 缓存装饰器
def cache_result(key_prefix: str, expire: int = 3600):
    """缓存结果装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = await redis_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await redis_manager.set(cache_key, result, expire)
            return result
        
        return wrapper
    return decorator
