# 技术上下文文档

## 技术栈
### 前端技术栈
- **框架**: Vue 3.x
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4.x
- **UI组件库**: Element Plus / Ant Design Vue
- **HTTP客户端**: Axios
- **CSS预处理器**: SCSS/SASS
- **代码规范**: ESLint + Prettier

### 后端技术栈
- **框架**: FastAPI
- **Python版本**: 3.12.10
- **ORM**: SQLAlchemy 2.x
- **数据库迁移**: Alembic
- **异步支持**: asyncio + asyncpg
- **API文档**: OpenAPI (Swagger)
- **数据验证**: Pydantic
- **测试框架**: pytest

### 数据库技术
- **关系数据库**: MySQL 8.0.43
- **缓存数据库**: Redis 8.0.3
- **连接池**: aiomysql + aioredis
- **数据库驱动**: PyMySQL / aiomysql

### 安全技术
- **国密算法**: SM2 (非对称加密) + SM4 (对称加密)
- **密码哈希**: bcrypt + 自定义盐
- **JWT**: PyJWT
- **CORS**: FastAPI CORS中间件

## 开发环境配置
### Python环境
- **虚拟环境**: virtualenv
- **环境路径**: D:\envSoft\python_envs\rbacplus
- **包管理**: pip
- **依赖文件**: requirements.txt

### 前端环境
- **Node.js**: 18.x+
- **包管理**: npm / yarn / pnpm
- **依赖文件**: package.json

### 开发工具
- **IDE**: VS Code / PyCharm
- **版本控制**: Git
- **API测试**: Postman / Insomnia
- **数据库工具**: MySQL Workbench / DBeaver

## 技术限制条件
### 性能限制
- **并发连接**: 数据库连接池限制
- **内存使用**: Redis内存限制
- **文件上传**: 单文件大小限制
- **API频率**: 接口调用频率限制

### 安全限制
- **密码策略**: 最少8位，包含大小写字母和数字
- **用户名规则**: 5-32字符，不含特殊字符
- **会话超时**: JWT Token过期时间
- **IP限制**: 可配置IP白名单

### 业务限制
- **软删除**: 所有数据必须支持软删除
- **关系表**: 多对多关系必须使用独立关系表
- **编码**: 统一使用UTF-8编码
- **审计**: 所有操作必须记录审计日志

## 依赖项清单
### 后端核心依赖
```
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
sqlalchemy>=2.0.0
alembic>=1.12.0
pydantic>=2.4.0
python-multipart
python-jose[cryptography]
passlib[bcrypt]
aioredis>=2.0.0
aiomysql>=0.2.0
```

### 前端核心依赖
```
vue@^3.3.0
vue-router@^4.2.0
pinia@^2.1.0
element-plus@^2.4.0
axios@^1.5.0
@vueuse/core@^10.5.0
```

### 开发依赖
```
# 后端开发依赖
pytest>=7.4.0
pytest-asyncio
black
flake8
mypy

# 前端开发依赖
@vitejs/plugin-vue
vite
typescript
eslint
prettier
```

## 工具使用规范
### 代码规范
- **Python**: 遵循PEP 8规范，使用black格式化
- **JavaScript/Vue**: 使用ESLint + Prettier
- **提交规范**: 使用Conventional Commits

### 测试规范
- **单元测试**: 覆盖率要求80%+
- **集成测试**: 关键业务流程必须有集成测试
- **API测试**: 所有API接口必须有测试用例
- **测试数据**: 使用UTF-8编码构建测试数据

### 部署规范
- **环境隔离**: 开发、测试、生产环境严格隔离
- **配置管理**: 使用环境变量管理配置
- **日志管理**: 统一日志格式和级别
- **监控告警**: 关键指标监控和告警

### 文档规范
- **API文档**: 使用OpenAPI自动生成
- **代码注释**: 关键逻辑必须有注释
- **变更记录**: 重要变更必须记录在开发文档中
- **部署文档**: 详细的部署和运维文档
