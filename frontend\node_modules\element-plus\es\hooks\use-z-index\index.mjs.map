{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-z-index/index.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, ref, unref } from 'vue'\nimport { debugWarn, isClient, isNumber } from '@element-plus/utils'\n\nimport type { InjectionKey, Ref } from 'vue'\n\nexport interface ElZIndexInjectionContext {\n  current: number\n}\n\nconst initial: ElZIndexInjectionContext = {\n  current: 0,\n}\n\nconst zIndex = ref(0)\n\nexport const defaultInitialZIndex = 2000\n\n// For SSR\nexport const ZINDEX_INJECTION_KEY: InjectionKey<ElZIndexInjectionContext> =\n  Symbol('elZIndexContextKey')\n\nexport const zIndexContextKey: InjectionKey<Ref<number | undefined>> =\n  Symbol('zIndexContextKey')\n\nexport const useZIndex = (zIndexOverrides?: Ref<number>) => {\n  const increasingInjection = getCurrentInstance()\n    ? inject(ZINDEX_INJECTION_KEY, initial)\n    : initial\n\n  const zIndexInjection =\n    zIndexOverrides ||\n    (getCurrentInstance() ? inject(zIndexContextKey, undefined) : undefined)\n\n  const initialZIndex = computed(() => {\n    const zIndexFromInjection = unref(zIndexInjection)\n    return isNumber(zIndexFromInjection)\n      ? zIndexFromInjection\n      : defaultInitialZIndex\n  })\n\n  const currentZIndex = computed(() => initialZIndex.value + zIndex.value)\n\n  const nextZIndex = () => {\n    increasingInjection.current++\n    zIndex.value = increasingInjection.current\n    return currentZIndex.value\n  }\n\n  if (!isClient && !inject(ZINDEX_INJECTION_KEY)) {\n    debugWarn(\n      'ZIndexInjection',\n      `Looks like you are using server rendering, you must provide a z-index provider to ensure the hydration process to be succeed\nusage: app.provide(ZINDEX_INJECTION_KEY, { current: 0 })`\n    )\n  }\n\n  return {\n    initialZIndex,\n    currentZIndex,\n    nextZIndex,\n  }\n}\n\nexport type UseZIndexReturn = ReturnType<typeof useZIndex>\n"], "names": [], "mappings": ";;;;AAEA,MAAM,OAAO,GAAG;AAChB,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AACF,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACV,MAAC,oBAAoB,GAAG,IAAI;AAC5B,MAAC,oBAAoB,GAAG,MAAM,CAAC,oBAAoB,EAAE;AACrD,MAAC,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,EAAE;AAC/C,MAAC,SAAS,GAAG,CAAC,eAAe,KAAK;AAC9C,EAAE,MAAM,mBAAmB,GAAG,kBAAkB,EAAE,GAAG,MAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;AACrG,EAAE,MAAM,eAAe,GAAG,eAAe,KAAK,kBAAkB,EAAE,GAAG,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAChH,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM;AACvC,IAAI,MAAM,mBAAmB,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC;AACvD,IAAI,OAAO,QAAQ,CAAC,mBAAmB,CAAC,GAAG,mBAAmB,GAAG,oBAAoB,CAAC;AACtF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC3E,EAAE,MAAM,UAAU,GAAG,MAAM;AAC3B,IAAI,mBAAmB,CAAC,OAAO,EAAE,CAAC;AAClC,IAAI,MAAM,CAAC,KAAK,GAAG,mBAAmB,CAAC,OAAO,CAAC;AAC/C,IAAI,OAAO,aAAa,CAAC,KAAK,CAAC;AAC/B,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,CAG/C;AACH,EAAE,OAAO;AACT,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,UAAU;AACd,GAAG,CAAC;AACJ;;;;"}