{"version": 3, "file": "use-input-tag.mjs", "sources": ["../../../../../../../packages/components/input-tag/src/composables/use-input-tag.ts"], "sourcesContent": ["import { computed, ref, shallowRef, watch } from 'vue'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { debugWarn, ensureArray, isUndefined } from '@element-plus/utils'\nimport { useComposition, useFocusController } from '@element-plus/hooks'\nimport { useFormDisabled, useFormSize } from '@element-plus/components/form'\n\nimport type { EmitFn } from '@element-plus/utils'\nimport type { FormItemContext } from '@element-plus/components/form'\nimport type { InputTagEmits, InputTagProps } from '../input-tag'\n\ninterface UseInputTagOptions {\n  props: InputTagProps\n  emit: EmitFn<InputTagEmits>\n  formItem?: FormItemContext\n}\n\nexport function useInputTag({ props, emit, formItem }: UseInputTagOptions) {\n  const disabled = useFormDisabled()\n  const size = useFormSize()\n\n  const inputRef = shallowRef<HTMLInputElement>()\n  const inputValue = ref<string>()\n\n  const tagSize = computed(() => {\n    return ['small'].includes(size.value) ? 'small' : 'default'\n  })\n  const placeholder = computed(() => {\n    return props.modelValue?.length ? undefined : props.placeholder\n  })\n  const closable = computed(() => !(props.readonly || disabled.value))\n  const inputLimit = computed(() => {\n    return isUndefined(props.max)\n      ? false\n      : (props.modelValue?.length ?? 0) >= props.max\n  })\n\n  const addTagsEmit = (value: string | string[]) => {\n    const list = [...(props.modelValue ?? []), ...ensureArray(value)]\n\n    emit(UPDATE_MODEL_EVENT, list)\n    emit(CHANGE_EVENT, list)\n    emit('add-tag', value)\n    inputValue.value = undefined\n  }\n\n  const getDelimitedTags = (input: string) => {\n    const tags = input\n      .split(props.delimiter)\n      .filter((val) => val && val !== input)\n    if (props.max) {\n      const maxInsert = props.max - (props.modelValue?.length ?? 0)\n      tags.splice(maxInsert)\n    }\n    return tags.length === 1 ? tags[0] : tags\n  }\n\n  const handleInput = (event: Event) => {\n    if (inputLimit.value) {\n      inputValue.value = undefined\n      return\n    }\n\n    if (isComposing.value) return\n    if (props.delimiter && inputValue.value) {\n      const tags = getDelimitedTags(inputValue.value)\n      if (tags.length) {\n        addTagsEmit(tags)\n      }\n    }\n    emit(INPUT_EVENT, (event.target as HTMLInputElement).value)\n  }\n\n  const handleKeydown = (event: KeyboardEvent) => {\n    if (isComposing.value) return\n    switch (event.code) {\n      case props.trigger:\n        event.preventDefault()\n        event.stopPropagation()\n        handleAddTag()\n        break\n      case EVENT_CODE.numpadEnter:\n        if (props.trigger === EVENT_CODE.enter) {\n          event.preventDefault()\n          event.stopPropagation()\n          handleAddTag()\n        }\n        break\n      case EVENT_CODE.backspace:\n        if (!inputValue.value && props.modelValue?.length) {\n          event.preventDefault()\n          event.stopPropagation()\n          handleRemoveTag(props.modelValue.length - 1)\n        }\n        break\n    }\n  }\n\n  const handleAddTag = () => {\n    const value = inputValue.value?.trim()\n    if (!value || inputLimit.value) return\n    addTagsEmit(value)\n  }\n\n  const handleRemoveTag = (index: number) => {\n    const value = (props.modelValue ?? []).slice()\n    const [item] = value.splice(index, 1)\n\n    emit(UPDATE_MODEL_EVENT, value)\n    emit(CHANGE_EVENT, value)\n    emit('remove-tag', item)\n  }\n\n  const handleClear = () => {\n    inputValue.value = undefined\n    emit(UPDATE_MODEL_EVENT, undefined)\n    emit(CHANGE_EVENT, undefined)\n    emit('clear')\n  }\n\n  const handleDragged = (\n    draggingIndex: number,\n    dropIndex: number,\n    type: 'before' | 'after'\n  ) => {\n    const value = (props.modelValue ?? []).slice()\n    const [draggedItem] = value.splice(draggingIndex, 1)\n    const step =\n      dropIndex > draggingIndex && type === 'before'\n        ? -1\n        : dropIndex < draggingIndex && type === 'after'\n        ? 1\n        : 0\n\n    value.splice(dropIndex + step, 0, draggedItem)\n    emit(UPDATE_MODEL_EVENT, value)\n    emit(CHANGE_EVENT, value)\n  }\n\n  const focus = () => {\n    inputRef.value?.focus()\n  }\n\n  const blur = () => {\n    inputRef.value?.blur()\n  }\n\n  const { wrapperRef, isFocused } = useFocusController(inputRef, {\n    disabled,\n    afterBlur() {\n      if (props.saveOnBlur) {\n        handleAddTag()\n      } else {\n        inputValue.value = undefined\n      }\n\n      if (props.validateEvent) {\n        formItem?.validate?.('blur').catch((err) => debugWarn(err))\n      }\n    },\n  })\n\n  const {\n    isComposing,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd,\n  } = useComposition({ afterComposition: handleInput })\n\n  watch(\n    () => props.modelValue,\n    () => {\n      if (props.validateEvent) {\n        formItem?.validate?.(CHANGE_EVENT).catch((err) => debugWarn(err))\n      }\n    }\n  )\n\n  return {\n    inputRef,\n    wrapperRef,\n    isFocused,\n    isComposing,\n    inputValue,\n    size,\n    tagSize,\n    placeholder,\n    closable,\n    disabled,\n    inputLimit,\n    handleDragged,\n    handleInput,\n    handleKeydown,\n    handleAddTag,\n    handleRemoveTag,\n    handleClear,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd,\n    focus,\n    blur,\n  }\n}\n"], "names": ["ensureArray"], "mappings": ";;;;;;;;;;AAUO,SAAS,WAAW,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;AACvD,EAAE,MAAM,QAAQ,GAAG,eAAe,EAAE,CAAC;AACrC,EAAE,MAAM,IAAI,GAAG,WAAW,EAAE,CAAC;AAC7B,EAAE,MAAM,QAAQ,GAAG,UAAU,EAAE,CAAC;AAChC,EAAE,MAAM,UAAU,GAAG,GAAG,EAAE,CAAC;AAC3B,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM;AACjC,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;AAChE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC;AAC/F,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACvE,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC;AACxI,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,GAAGA,SAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AACvF,IAAI,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;AACnC,IAAI,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAC3B,IAAI,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AAC9B,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,KAAK,KAAK;AACtC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC,CAAC;AACpF,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE;AACnB,MAAM,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AACnH,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC9C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,IAAI,UAAU,CAAC,KAAK,EAAE;AAC1B,MAAM,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AAChC,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,WAAW,CAAC,KAAK;AACzB,MAAM,OAAO;AACb,IAAI,IAAI,KAAK,CAAC,SAAS,IAAI,UAAU,CAAC,KAAK,EAAE;AAC7C,MAAM,MAAM,IAAI,GAAG,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACtD,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;AACvB,QAAQ,WAAW,CAAC,IAAI,CAAC,CAAC;AAC1B,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC1C,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;AACnC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,WAAW,CAAC,KAAK;AACzB,MAAM,OAAO;AACb,IAAI,QAAQ,KAAK,CAAC,IAAI;AACtB,MAAM,KAAK,KAAK,CAAC,OAAO;AACxB,QAAQ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC/B,QAAQ,KAAK,CAAC,eAAe,EAAE,CAAC;AAChC,QAAQ,YAAY,EAAE,CAAC;AACvB,QAAQ,MAAM;AACd,MAAM,KAAK,UAAU,CAAC,WAAW;AACjC,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,UAAU,CAAC,KAAK,EAAE;AAChD,UAAU,KAAK,CAAC,cAAc,EAAE,CAAC;AACjC,UAAU,KAAK,CAAC,eAAe,EAAE,CAAC;AAClC,UAAU,YAAY,EAAE,CAAC;AACzB,SAAS;AACT,QAAQ,MAAM;AACd,MAAM,KAAK,UAAU,CAAC,SAAS;AAC/B,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE;AACzF,UAAU,KAAK,CAAC,cAAc,EAAE,CAAC;AACjC,UAAU,KAAK,CAAC,eAAe,EAAE,CAAC;AAClC,UAAU,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACvD,SAAS;AACT,QAAQ,MAAM;AACd,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,KAAK,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AACvE,IAAI,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK;AAClC,MAAM,OAAO;AACb,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AACvB,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC,KAAK,KAAK;AACrC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC;AACtE,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAC7B,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAClB,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,KAAK;AAC5D,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC;AACtE,IAAI,MAAM,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AACzD,IAAI,MAAM,IAAI,GAAG,SAAS,GAAG,aAAa,IAAI,IAAI,KAAK,QAAQ,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,aAAa,IAAI,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7H,IAAI,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAC9B,GAAG,CAAC;AACJ,EAAE,MAAM,KAAK,GAAG,MAAM;AACtB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AACxD,GAAG,CAAC;AACJ,EAAE,MAAM,IAAI,GAAG,MAAM;AACrB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AACvD,GAAG,CAAC;AACJ,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,kBAAkB,CAAC,QAAQ,EAAE;AACjE,IAAI,QAAQ;AACZ,IAAI,SAAS,GAAG;AAChB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,IAAI,KAAK,CAAC,UAAU,EAAE;AAC5B,QAAQ,YAAY,EAAE,CAAC;AACvB,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AAClC,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,aAAa,EAAE;AAC/B,QAAQ,CAAC,EAAE,GAAG,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,SAAS,CAAI,CAAC,CAAC,CAAC;AACzI,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM;AACR,IAAI,WAAW;AACf,IAAI,sBAAsB;AAC1B,IAAI,uBAAuB;AAC3B,IAAI,oBAAoB;AACxB,GAAG,GAAG,cAAc,CAAC,EAAE,gBAAgB,EAAE,WAAW,EAAE,CAAC,CAAC;AACxD,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,MAAM;AACtC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,KAAK,CAAC,aAAa,EAAE;AAC7B,MAAM,CAAC,EAAE,GAAG,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,SAAS,CAAI,CAAC,CAAC,CAAC;AAC7I,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,IAAI;AACR,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,sBAAsB;AAC1B,IAAI,uBAAuB;AAC3B,IAAI,oBAAoB;AACxB,IAAI,KAAK;AACT,IAAI,IAAI;AACR,GAAG,CAAC;AACJ;;;;"}