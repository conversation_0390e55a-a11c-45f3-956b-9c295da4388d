{"version": 3, "file": "divider.mjs", "sources": ["../../../../../../packages/components/divider/src/divider.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[ns.b(), ns.m(direction)]\"\n    :style=\"dividerStyle\"\n    role=\"separator\"\n  >\n    <div\n      v-if=\"$slots.default && direction !== 'vertical'\"\n      :class=\"[ns.e('text'), ns.is(contentPosition)]\"\n    >\n      <slot />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { dividerProps } from './divider'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElDivider',\n})\nconst props = defineProps(dividerProps)\nconst ns = useNamespace('divider')\nconst dividerStyle = computed(() => {\n  return ns.cssVar({\n    'border-style': props.borderStyle,\n  }) as CSSProperties\n})\n</script>\n"], "names": [], "mappings": ";;;;;mCAsBc,CAAA;AAAA,EACZ,IAAM,EAAA,WAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAEA,IAAM,MAAA,EAAA,GAAK,aAAa,SAAS,CAAA,CAAA;AACjC,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAClC,MAAA,OAAO,GAAG,MAAO,CAAA;AAAA,QACf,gBAAgB,KAAM,CAAA,WAAA;AAAA,OACvB,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;"}