{"version": 3, "file": "panel-time-pick.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/time-picker-com/panel-time-pick.vue"], "sourcesContent": ["<template>\n  <transition :name=\"transitionName\">\n    <div v-if=\"actualVisible || visible\" :class=\"ns.b('panel')\">\n      <div :class=\"[ns.be('panel', 'content'), { 'has-seconds': showSeconds }]\">\n        <time-spinner\n          ref=\"spinner\"\n          :role=\"datetimeRole || 'start'\"\n          :arrow-control=\"arrowControl\"\n          :show-seconds=\"showSeconds\"\n          :am-pm-mode=\"amPmMode\"\n          :spinner-date=\"(parsedValue as any)\"\n          :disabled-hours=\"disabledHours\"\n          :disabled-minutes=\"disabledMinutes\"\n          :disabled-seconds=\"disabledSeconds\"\n          @change=\"handleChange\"\n          @set-option=\"onSetOption\"\n          @select-range=\"setSelectionRange\"\n        />\n      </div>\n      <div :class=\"ns.be('panel', 'footer')\">\n        <button\n          type=\"button\"\n          :class=\"[ns.be('panel', 'btn'), 'cancel']\"\n          @click=\"handleCancel\"\n        >\n          {{ t('el.datepicker.cancel') }}\n        </button>\n        <button\n          type=\"button\"\n          :class=\"[ns.be('panel', 'btn'), 'confirm']\"\n          @click=\"handleConfirm()\"\n        >\n          {{ t('el.datepicker.confirm') }}\n        </button>\n      </div>\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref } from 'vue'\nimport dayjs from 'dayjs'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { isUndefined } from '@element-plus/utils'\nimport { PICKER_BASE_INJECTION_KEY } from '../constants'\nimport { panelTimePickerProps } from '../props/panel-time-picker'\nimport { useTimePanel } from '../composables/use-time-panel'\nimport {\n  buildAvailableTimeSlotGetter,\n  useOldValue,\n} from '../composables/use-time-picker'\nimport TimeSpinner from './basic-time-spinner.vue'\n\nimport type { Dayjs } from 'dayjs'\n\nconst props = defineProps(panelTimePickerProps)\nconst emit = defineEmits(['pick', 'select-range', 'set-picker-option'])\n\n// Injections\nconst pickerBase = inject(PICKER_BASE_INJECTION_KEY) as any\nconst {\n  arrowControl,\n  disabledHours,\n  disabledMinutes,\n  disabledSeconds,\n  defaultValue,\n} = pickerBase.props\nconst { getAvailableHours, getAvailableMinutes, getAvailableSeconds } =\n  buildAvailableTimeSlotGetter(disabledHours, disabledMinutes, disabledSeconds)\n\nconst ns = useNamespace('time')\nconst { t, lang } = useLocale()\n// data\nconst selectionRange = ref([0, 2])\nconst oldValue = useOldValue(props)\n// computed\nconst transitionName = computed(() => {\n  return isUndefined(props.actualVisible)\n    ? `${ns.namespace.value}-zoom-in-top`\n    : ''\n})\nconst showSeconds = computed(() => {\n  return props.format.includes('ss')\n})\nconst amPmMode = computed(() => {\n  if (props.format.includes('A')) return 'A'\n  if (props.format.includes('a')) return 'a'\n  return ''\n})\n// method\nconst isValidValue = (_date: Dayjs) => {\n  const parsedDate = dayjs(_date).locale(lang.value)\n  const result = getRangeAvailableTime(parsedDate)\n  return parsedDate.isSame(result)\n}\nconst handleCancel = () => {\n  emit('pick', oldValue.value, false)\n}\nconst handleConfirm = (visible = false, first = false) => {\n  if (first) return\n  emit('pick', props.parsedValue, visible)\n}\nconst handleChange = (_date: Dayjs) => {\n  // visible avoids edge cases, when use scrolls during panel closing animation\n  if (!props.visible) {\n    return\n  }\n  const result = getRangeAvailableTime(_date).millisecond(0)\n  emit('pick', result, true)\n}\n\nconst setSelectionRange = (start: number, end: number) => {\n  emit('select-range', start, end)\n  selectionRange.value = [start, end]\n}\n\nconst changeSelectionRange = (step: number) => {\n  const actualFormat = props.format\n  const hourIndex = actualFormat.indexOf('HH')\n  const minuteIndex = actualFormat.indexOf('mm')\n  const secondIndex = actualFormat.indexOf('ss')\n  const list: number[] = []\n  const mapping: string[] = []\n  if (hourIndex !== -1) {\n    list.push(hourIndex)\n    mapping.push('hours')\n  }\n  if (minuteIndex !== -1) {\n    list.push(minuteIndex)\n    mapping.push('minutes')\n  }\n  if (secondIndex !== -1 && showSeconds.value) {\n    list.push(secondIndex)\n    mapping.push('seconds')\n  }\n\n  const index = list.indexOf(selectionRange.value[0])\n  const next = (index + step + list.length) % list.length\n  timePickerOptions['start_emitSelectRange'](mapping[next])\n}\n\nconst handleKeydown = (event: KeyboardEvent) => {\n  const code = event.code\n\n  const { left, right, up, down } = EVENT_CODE\n\n  if ([left, right].includes(code)) {\n    const step = code === left ? -1 : 1\n    changeSelectionRange(step)\n    event.preventDefault()\n    return\n  }\n\n  if ([up, down].includes(code)) {\n    const step = code === up ? -1 : 1\n    timePickerOptions['start_scrollDown'](step)\n    event.preventDefault()\n    return\n  }\n}\n\nconst { timePickerOptions, onSetOption, getAvailableTime } = useTimePanel({\n  getAvailableHours,\n  getAvailableMinutes,\n  getAvailableSeconds,\n})\n\nconst getRangeAvailableTime = (date: Dayjs) => {\n  return getAvailableTime(date, props.datetimeRole || '', true)\n}\n\nconst parseUserInput = (value: Dayjs) => {\n  if (!value) return null\n  return dayjs(value, props.format).locale(lang.value)\n}\n\nconst formatToString = (value: Dayjs) => {\n  if (!value) return null\n  return value.format(props.format)\n}\n\nconst getDefaultValue = () => {\n  return dayjs(defaultValue).locale(lang.value)\n}\n\nemit('set-picker-option', ['isValidValue', isValidValue])\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['handleKeydownInput', handleKeydown])\nemit('set-picker-option', ['getRangeAvailableTime', getRangeAvailableTime])\nemit('set-picker-option', ['getDefaultValue', getDefaultValue])\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA4DA,IAAM,MAAA,UAAA,GAAa,OAAO,yBAAyB,CAAA,CAAA;AACnD,IAAM,MAAA;AAAA,MACJ,YAAA;AAAA,MACA,aAAA;AAAA,MACA,eAAA;AAAA,MACA,eAAA;AAAA,MACA,YAAA;AAAA,QACE,UAAW,CAAA,KAAA,CAAA;AACf,IAAM,MAAA,EAAE,mBAAmB,mBAAqB,EAAA,mBAAA,KAC9C,4BAA6B,CAAA,aAAA,EAAe,iBAAiB,eAAe,CAAA,CAAA;AAE9E,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAC9B,IAAA,MAAM,EAAE,CAAA,EAAG,IAAK,EAAA,GAAI,SAAU,EAAA,CAAA;AAE9B,IAAA,MAAM,cAAiB,GAAA,GAAA,CAAI,CAAC,CAAA,EAAG,CAAC,CAAC,CAAA,CAAA;AACjC,IAAM,MAAA,QAAA,GAAW,YAAY,KAAK,CAAA,CAAA;AAElC,IAAM,MAAA,cAAA,GAAiB,SAAS,MAAM;AACpC,MAAO,OAAA,WAAA,CAAY,MAAM,aAAa,CAAA,GAClC,GAAG,EAAG,CAAA,SAAA,CAAU,KAAK,CACrB,YAAA,CAAA,GAAA,EAAA,CAAA;AAAA,KACL,CAAA,CAAA;AACD,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAO,OAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,IAAI,CAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AACD,IAAM,MAAA,QAAA,GAAW,SAAS,MAAM;AAC9B,MAAA,IAAI,KAAM,CAAA,MAAA,CAAO,QAAS,CAAA,GAAG;AAC7B,QAAA,OAAU,GAAA,CAAA;AACV,MAAO,IAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA;AAAA,QACR,OAAA,GAAA,CAAA;AAED,MAAM,OAAA,EAAA,CAAA;AACJ,KAAA,CAAA,CAAA;AACA,IAAM,MAAA;AACN,MAAO,MAAA,UAAA,SAAkB,KAAM,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACjC,MAAA,MAAA,GAAA,qBAAA,CAAA,UAAA,CAAA,CAAA;AACA,MAAA,wBAA2B,CAAA,MAAA,CAAA,CAAA;AACzB,KAAK,CAAA;AAA6B,IACpC,MAAA,YAAA,GAAA,MAAA;AACA,MAAA,IAAM,CAAgB,MAAA,EAAA,QAAC,CAAU,KAAA,EAAA,KAAA,CAAA,CAAA;AAC/B,KAAA,CAAA;AACA,IAAK,MAAA,aAAc,GAAA,CAAA,OAAA,GAAA,KAAoB,EAAA,KAAA,GAAA,KAAA,KAAA;AAAA,MACzC,IAAA,KAAA;AACA,QAAM,OAAA;AAEJ,MAAI,IAAA,CAAC,MAAM,EAAS,KAAA,CAAA,WAAA,EAAA,OAAA,CAAA,CAAA;AAClB,KAAA,CAAA;AAAA,IACF,MAAA,YAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAA,IAAA,CAAA,KAAe,CAAA,OAAA,EAAA;AACf,QAAK,OAAA;AAAoB,OAC3B;AAEA,MAAM,MAAA,MAAA,GAAA,qBAAoD,CAAA,KAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA;AACxD,MAAK,IAAA,CAAA,MAAA,EAAA,MAAA,EAAgB;AACrB,KAAe,CAAA;AAAmB,IACpC,MAAA,iBAAA,GAAA,CAAA,KAAA,EAAA,GAAA,KAAA;AAEA,MAAM,IAAA,CAAA,cAAA,EAAA,KAAA,EAAwB,GAAiB,CAAA,CAAA;AAC7C,MAAA,uBAA2B,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AAC3B,KAAM,CAAA;AACN,IAAM,MAAA,oBAA2B,GAAA,CAAA,IAAA,KAAA;AACjC,MAAM,MAAA,YAAA,GAA2B,KAAA,CAAA,MAAA,CAAA;AACjC,MAAA,MAAM,SAAkB,GAAA,YAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AACxB,MAAA,MAAM,WAAqB,GAAA,YAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AAC3B,MAAA,oBAAsB,YAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AACpB,MAAA,MAAA,OAAmB,EAAA,CAAA;AACnB,MAAA,MAAA,UAAoB,EAAA,CAAA;AAAA,MACtB,IAAA,SAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,mBAAwB,CAAA,CAAA;AACtB,QAAA,YAAqB,CAAA,OAAA,CAAA,CAAA;AACrB,OAAA;AAAsB,MACxB,IAAA,WAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAI,IAAA,CAAA,IAAA,CAAA,WAAsB,CAAA,CAAA;AACxB,QAAA,YAAqB,CAAA,SAAA,CAAA,CAAA;AACrB,OAAA;AAAsB,MACxB,IAAA,WAAA,KAAA,CAAA,CAAA,IAAA,WAAA,CAAA,KAAA,EAAA;AAEA,QAAA,IAAM,iBAAa,CAAA,CAAA;AACnB,QAAA,OAAc,CAAA,IAAA,CAAA,SAAe,CAAA,CAAA;AAC7B,OAAA;AAAwD,MAC1D,MAAA,KAAA,GAAA,IAAA,CAAA,OAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,MAAM,MAAA,IAAA,GAAA,CAAA,KAAA,GAA0C,IAAA,GAAA,IAAA,CAAA,MAAA,IAAA,IAAA,CAAA,MAAA,CAAA;AAC9C,MAAA,iBAAmB,CAAA,uBAAA,CAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAEnB,KAAA,CAAA;AAEA,IAAA,MAAI,aAAc,GAAA,CAAA,KAAA,KAAa;AAC7B,MAAM,MAAA,IAAA,GAAA,KAAgB,CAAA,IAAA,CAAA;AACtB,MAAA,MAAA,EAAA,IAAA,EAAA,KAAA,EAAA,EAAqB,EAAI,IAAA,EAAA,GAAA,UAAA,CAAA;AACzB,MAAA,IAAA,CAAA,IAAqB,EAAA,KAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AACrB,QAAA,MAAA,IAAA,GAAA,IAAA,KAAA,IAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AAAA,QACF,oBAAA,CAAA,IAAA,CAAA,CAAA;AAEA,QAAA,KAAS,CAAA,cAAM,EAAS;AACtB,QAAM,OAAA;AACN,OAAkB;AAClB,MAAA,IAAA,CAAA,EAAA,EAAqB,IAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AACrB,QAAA,MAAA,IAAA,GAAA,IAAA,KAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AAAA,QACF,iBAAA,CAAA,kBAAA,CAAA,CAAA,IAAA,CAAA,CAAA;AAAA,QACF,KAAA,CAAA,cAAA,EAAA,CAAA;AAEA,QAAA,OAAQ;AAAkE,OACxE;AAAA,KACA,CAAA;AAAA,IACA,MAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,GAAA,YAAA,CAAA;AAAA,MACD,iBAAA;AAED,MAAM,mBAAA;AACJ,MAAA,mBAAwB;AAAoC,KAC9D,CAAA,CAAA;AAEA,IAAM,MAAA,qBAAmC,GAAA,CAAA,IAAA,KAAA;AACvC,MAAI,uBAAe,CAAA,IAAA,EAAA,KAAA,CAAA,YAAA,IAAA,EAAA,EAAA,IAAA,CAAA,CAAA;AACnB,KAAA,CAAA;AAAmD,IACrD,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,IAAA,CAAA,KAAA;AACJ,QAAI,WAAe,CAAA;AACnB,MAAO,OAAA,KAAA,CAAM,KAAO,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KAClC,CAAA;AAEA,IAAA,MAAM,kBAAkB,KAAM,KAAA;AAC5B,MAAA,IAAA,CAAA,KAAa;AAA+B,QAC9C,OAAA,IAAA,CAAA;AAEA,MAAA,OAA0B,KAAA,CAAA,MAAA,CAAA,KAAC,CAAgB,MAAA,CAAA,CAAA;AAC3C,KAAA,CAAA;AACA,IAAA,MAA0B,eAAA,GAAA,MAAmB;AAC7C,MAAA,OAA0B,KAAA,CAAA,YAAC,CAAsB,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACjD,KAAA,CAAA;AACA,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,cAAmB,EAAA,YAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}