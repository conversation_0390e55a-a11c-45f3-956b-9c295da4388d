# 系统模式文档

## 系统架构设计
### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)   │    │  后端 (FastAPI) │    │  数据库 (MySQL) │
│                 │    │                 │    │                 │
│ - 用户界面      │◄──►│ - API接口       │◄──►│ - 用户数据      │
│ - 权限控制      │    │ - 业务逻辑      │    │ - 权限数据      │
│ - 状态管理      │    │ - 数据验证      │    │ - 审计日志      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   缓存 (Redis)  │
                       │                 │
                       │ - 会话管理      │
                       │ - 临时数据      │
                       │ - 缓存策略      │
                       └─────────────────┘
```

### 分层架构
1. **表现层 (Presentation Layer)**
   - Vue3组件
   - 路由管理
   - 状态管理 (Pinia)
   - UI组件库

2. **API层 (API Layer)**
   - FastAPI路由
   - 请求验证
   - 响应格式化
   - 错误处理

3. **业务逻辑层 (Business Logic Layer)**
   - 服务类 (Services)
   - 业务规则
   - 权限验证
   - 数据处理

4. **数据访问层 (Data Access Layer)**
   - SQLAlchemy ORM
   - 数据库模型
   - 查询优化
   - 事务管理

5. **数据存储层 (Data Storage Layer)**
   - MySQL数据库
   - Redis缓存
   - 文件存储

## 关键技术决策
### 1. 安全架构
- **传输安全**: HTTPS + SM2加密
- **存储安全**: SM4加密 + 加盐哈希
- **会话安全**: JWT Token + Redis存储
- **权限控制**: RBAC模型 + 细粒度权限

### 2. 数据架构
- **软删除**: 所有表包含deleted_at字段
- **关系管理**: 独立关系表管理多对多关系
- **审计追踪**: 操作日志和访问日志分离
- **数据完整性**: 外键约束 + 业务验证

### 3. 缓存策略
- **会话缓存**: 用户登录状态和权限信息
- **数据缓存**: 频繁查询的字典数据
- **页面缓存**: 静态内容和菜单结构
- **失效策略**: TTL + 主动失效

## 采用的设计模式
### 1. MVC模式
- **Model**: SQLAlchemy模型
- **View**: Vue3组件
- **Controller**: FastAPI路由处理器

### 2. 服务层模式
- 业务逻辑封装在服务类中
- 控制器只负责请求处理和响应
- 服务类可复用和测试

### 3. 仓储模式
- 数据访问逻辑封装
- 支持不同数据源切换
- 便于单元测试

### 4. 工厂模式
- 数据库连接工厂
- 服务实例工厂
- 配置对象工厂

## 组件交互关系
### 前端组件交互
```
App.vue
├── Router (vue-router)
├── Store (pinia)
├── Layout Components
│   ├── Header
│   ├── Sidebar
│   └── Footer
└── Page Components
    ├── User Management
    ├── Role Management
    └── Permission Management
```

### 后端模块交互
```
main.py
├── routers/
│   ├── auth.py
│   ├── users.py
│   ├── roles.py
│   └── permissions.py
├── services/
│   ├── auth_service.py
│   ├── user_service.py
│   └── permission_service.py
├── models/
│   ├── user.py
│   ├── role.py
│   └── permission.py
└── core/
    ├── config.py
    ├── database.py
    └── security.py
```

## 核心实现路径
1. **认证流程**: 登录 → 验证 → 生成Token → 缓存权限
2. **权限验证**: 请求 → 解析Token → 检查权限 → 允许/拒绝
3. **数据操作**: 请求 → 验证 → 业务处理 → 数据库操作 → 审计记录
4. **缓存更新**: 数据变更 → 失效缓存 → 重新加载 → 更新缓存
