# 开发环境文档

## 基础环境
- **操作系统**: Windows 11 家庭版
- **默认终端**: PowerShell
- **Python版本**: 3.12.10
- **Python启动终端**: cmd
- **Python虚拟环境**: virtualenv
- **虚拟环境名称**: rbacplus
- **虚拟环境路径**: D:\envSoft\python_envs\rbacplus\Scripts\python.exe

## 项目路径
- **项目根目录**: C:\Users\<USER>\Documents\augment-projects\rbacplus
- **前端路径**: C:\Users\<USER>\Documents\augment-projects\rbacplus\frontend
- **后端路径**: C:\Users\<USER>\Documents\augment-projects\rbacplus\backend
- **测试脚本目录**: C:\Users\<USER>\Documents\augment-projects\rbacplus\backend\测试脚本
- **开发文档目录**: C:\Users\<USER>\Documents\augment-projects\rbacplus\开发文档

## 端口配置
### 开发环境
- **前端端口**: 5200
- **后端端口**: 5300

### 生产环境
- **前端端口**: 7200
- **后端端口**: 7300

## 数据库配置
### 开发数据库
- **类型**: MySQL 8.0.43
- **连接字符串**: mysql://rbacpuls_devuser:m4itRTE7hVz4tSQLQLGJ@**************:8043/rbacpuls_dev

### 生产数据库
- **类型**: MySQL 8.0.43
- **连接字符串**: mysql://rbacplus_produser:6YNoxnshxxoahgjdpzml@**************:8043/rbacplus_prod

## Redis配置
### 开发Redis
- **版本**: Redis 8.0.3
- **连接字符串**: redis://:LxhgKEP5DJICDZNYNQLN@**************:6803/2

### 生产Redis
- **版本**: Redis 8.0.3
- **连接字符串**: redis://:LxhgKEP5DJICDZNYNQLN@**************:6803/12

## 开发规范
- 使用PowerShell风格的命令和语句
- 所有文件使用UTF-8编码
- 测试代码使用测试文件，不直接输出到终端
- 后端功能模块放在backend/apps文件夹下
