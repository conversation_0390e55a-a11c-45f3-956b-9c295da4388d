# 项目概要文档

## 项目名称
RBAC Plus - 基于RBAC的权限管理扩展系统

## 项目概述
这是一个基于RBAC（Role-Based Access Control）系统的扩展系统，采用前后端分离架构，提供完整的用户权限管理解决方案。

## 技术架构
- **前端**: Vue3
- **后端**: FastAPI
- **数据库**: MySQL 8.0.43
- **缓存**: Redis 8.0.3
- **架构模式**: 前后端分离

## 核心需求
1. **权限管理**: 基于RBAC模型的完整权限控制系统
2. **用户管理**: 用户注册、登录、信息管理
3. **角色管理**: 角色定义、权限分配、用户角色绑定
4. **菜单管理**: 动态菜单、权限控制导航
5. **审计日志**: 访问审计、操作审计
6. **系统管理**: 系统配置、数据字典、定时任务

## 项目目标
- 提供安全可靠的权限管理系统
- 支持灵活的角色权限配置
- 实现完整的审计追踪
- 提供友好的用户体验
- 支持系统的可扩展性

## 安全要求
- 密码传输使用SM2算法加密
- 密码存储使用SM4算法加密
- 启动时校验SM2密钥对
- 实现软删除功能
- 完整的审计日志记录

## 默认用户
- **管理员用户名**: area0
- **默认密码**: Ram10240

## 项目范围
包含15个核心功能模块，涵盖用户管理、权限控制、系统配置、审计监控等完整功能。
