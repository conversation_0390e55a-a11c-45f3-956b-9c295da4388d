{"version": 3, "file": "fixed-size-list.mjs", "sources": ["../../../../../../../packages/components/virtual-list/src/components/fixed-size-list.ts"], "sourcesContent": ["import { isString, throwError } from '@element-plus/utils'\nimport buildList from '../builders/build-list'\nimport { isHorizontal } from '../utils'\nimport {\n  AUTO_ALIGNMENT,\n  CENTERED_ALIGNMENT,\n  END_ALIGNMENT,\n  SMART_ALIGNMENT,\n  START_ALIGNMENT,\n} from '../defaults'\n\nimport type { VirtualizedListProps } from '../props'\n\ntype Props = VirtualizedListProps\n\nconst FixedSizeList = buildList({\n  name: 'ElFixedSizeList',\n  getItemOffset: ({ itemSize }, index) => index * (itemSize as number),\n\n  getItemSize: ({ itemSize }) => itemSize as number,\n\n  getEstimatedTotalSize: ({ total, itemSize }) => (itemSize as number) * total,\n\n  getOffset: (\n    { height, total, itemSize, layout, width },\n    index,\n    alignment,\n    scrollOffset\n  ) => {\n    const size = (isHorizontal(layout) ? width : height) as number\n    if (process.env.NODE_ENV !== 'production' && isString(size)) {\n      throwError(\n        '[ElVirtualList]',\n        `\n        You should set\n          width/height\n        to number when your layout is\n          horizontal/vertical\n      `\n      )\n    }\n    const lastItemOffset = Math.max(0, total * (itemSize as number) - size)\n    const maxOffset = Math.min(lastItemOffset, index * (itemSize as number))\n    const minOffset = Math.max(0, (index + 1) * (itemSize as number) - size)\n\n    if (alignment === SMART_ALIGNMENT) {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        alignment = AUTO_ALIGNMENT\n      } else {\n        alignment = CENTERED_ALIGNMENT\n      }\n    }\n\n    switch (alignment) {\n      case START_ALIGNMENT: {\n        return maxOffset\n      }\n      case END_ALIGNMENT: {\n        return minOffset\n      }\n      case CENTERED_ALIGNMENT: {\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(minOffset + (maxOffset - minOffset) / 2)\n        if (middleOffset < Math.ceil(size / 2)) {\n          return 0 // near the beginning\n        } else if (middleOffset > lastItemOffset + Math.floor(size / 2)) {\n          return lastItemOffset // near the end\n        } else {\n          return middleOffset\n        }\n      }\n      case AUTO_ALIGNMENT:\n      default: {\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset\n        } else if (scrollOffset < minOffset) {\n          return minOffset\n        } else {\n          return maxOffset\n        }\n      }\n    }\n  },\n\n  getStartIndexForOffset: ({ total, itemSize }, offset) =>\n    Math.max(0, Math.min(total - 1, Math.floor(offset / (itemSize as number)))),\n\n  getStopIndexForStartIndex: (\n    { height, total, itemSize, layout, width }: Props,\n    startIndex: number,\n    scrollOffset: number\n  ) => {\n    const offset = startIndex * (itemSize as number)\n    const size = isHorizontal(layout) ? width : height\n    const numVisibleItems = Math.ceil(\n      ((size as number) + scrollOffset - offset) / (itemSize as number)\n    )\n    return Math.max(\n      0,\n      Math.min(\n        total - 1,\n        // because startIndex is inclusive, so in order to prevent array outbound indexing\n        // we need to - 1 to prevent outbound behavior\n        startIndex + numVisibleItems - 1\n      )\n    )\n  },\n\n  /**\n   * Fixed size list does not need this cache\n   * Using any to bypass it, TODO: Using type inference to fix this.\n   */\n  initCache() {\n    return undefined as any\n  },\n\n  clearCache: true,\n\n  validateProps() {},\n})\n\nexport type FixedSizeListInstance = InstanceType<typeof FixedSizeList> & unknown\nexport default FixedSizeList\n"], "names": ["buildList"], "mappings": ";;;;AAUK,MAAC,aAAa,GAAGA,UAAS,CAAC;AAChC,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,aAAa,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,KAAK,KAAK,KAAK,GAAG,QAAQ;AAC1D,EAAE,WAAW,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,QAAQ;AACzC,EAAE,qBAAqB,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,QAAQ,GAAG,KAAK;AAClE,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,KAAK;AAC7F,IAAI,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;AASvD,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC;AAChE,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,GAAG,QAAQ,CAAC,CAAC;AACjE,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,CAAC;AACjE,IAAI,IAAI,SAAS,KAAK,eAAe,EAAE;AACvC,MAAM,IAAI,YAAY,IAAI,SAAS,GAAG,IAAI,IAAI,YAAY,IAAI,SAAS,GAAG,IAAI,EAAE;AAChF,QAAQ,SAAS,GAAG,cAAc,CAAC;AACnC,OAAO,MAAM;AACb,QAAQ,SAAS,GAAG,kBAAkB,CAAC;AACvC,OAAO;AACP,KAAK;AACL,IAAI,QAAQ,SAAS;AACrB,MAAM,KAAK,eAAe,EAAE;AAC5B,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO;AACP,MAAM,KAAK,aAAa,EAAE;AAC1B,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO;AACP,MAAM,KAAK,kBAAkB,EAAE;AAC/B,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,CAAC,CAAC;AACjF,QAAQ,IAAI,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE;AAChD,UAAU,OAAO,CAAC,CAAC;AACnB,SAAS,MAAM,IAAI,YAAY,GAAG,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE;AACzE,UAAU,OAAO,cAAc,CAAC;AAChC,SAAS,MAAM;AACf,UAAU,OAAO,YAAY,CAAC;AAC9B,SAAS;AACT,OAAO;AACP,MAAM,KAAK,cAAc,CAAC;AAC1B,MAAM,SAAS;AACf,QAAQ,IAAI,YAAY,IAAI,SAAS,IAAI,YAAY,IAAI,SAAS,EAAE;AACpE,UAAU,OAAO,YAAY,CAAC;AAC9B,SAAS,MAAM,IAAI,YAAY,GAAG,SAAS,EAAE;AAC7C,UAAU,OAAO,SAAS,CAAC;AAC3B,SAAS,MAAM;AACf,UAAU,OAAO,SAAS,CAAC;AAC3B,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,sBAAsB,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC1H,EAAE,yBAAyB,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,YAAY,KAAK;AACvG,IAAI,MAAM,MAAM,GAAG,UAAU,GAAG,QAAQ,CAAC;AACzC,IAAI,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;AACvD,IAAI,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,YAAY,GAAG,MAAM,IAAI,QAAQ,CAAC,CAAC;AACjF,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,UAAU,GAAG,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9E,GAAG;AACH,EAAE,SAAS,GAAG;AACd,IAAI,OAAO,KAAK,CAAC,CAAC;AAClB,GAAG;AACH,EAAE,UAAU,EAAE,IAAI;AAClB,EAAE,aAAa,GAAG;AAClB,GAAG;AACH,CAAC;;;;"}