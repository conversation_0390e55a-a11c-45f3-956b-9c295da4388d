{"version": 3, "file": "node-content.mjs", "sources": ["../../../../../../packages/components/cascader-panel/src/node-content.tsx"], "sourcesContent": ["import { Comment, defineComponent, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isArray } from '@element-plus/utils'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\n\nimport type { PropType, VNode } from 'vue'\nimport type { CascaderNode } from './types'\n\nfunction isVNodeEmpty(vnodes?: VNode[] | VNode) {\n  return !!(isArray(vnodes)\n    ? vnodes.every(({ type }) => type === Comment)\n    : vnodes?.type === Comment)\n}\n\nexport default defineComponent({\n  name: 'NodeContent',\n  props: {\n    node: {\n      type: Object as PropType<CascaderNode>,\n      required: true,\n    },\n    disabled: Boolean,\n  },\n  setup(props, { emit }) {\n    const ns = useNamespace('cascader-node')\n    const { config, renderLabelFn } = inject(CASCADER_PANEL_INJECTION_KEY)!\n    const { checkOnClickNode, checkOnClickLeaf } = config\n    const { node, disabled } = props\n    const { data, label: nodeLabel } = node\n\n    const label = () => {\n      const renderLabel = renderLabelFn?.({ node, data })\n      return isVNodeEmpty(renderLabel) ? nodeLabel : renderLabel ?? nodeLabel\n    }\n    function handleClick() {\n      if (\n        (checkOnClickNode || (node.isLeaf && checkOnClickLeaf)) &&\n        !disabled\n      ) {\n        emit('handleSelectCheck', !node.checked)\n      }\n    }\n    return () => (\n      <span class={ns.e('label')} onClick={handleClick}>\n        {label()}\n      </span>\n    )\n  },\n})\n"], "names": ["isVNodeEmpty", "vnodes", "type", "name", "props", "node", "required", "disabled", "Boolean", "emit", "renderLabelFn", "inject", "CASCADER_PANEL_INJECTION_KEY", "checkOnClickLeaf", "label", "nodeLabel", "renderLabel", "data"], "mappings": ";;;;;;AAQA,EAASA,OAAAA,CAAAA,EAAAA,OAAAA,CAAAA,MAAaC,CAAAA,GAA0B,MAAA,CAAA,KAAA,CAAA,CAAA;IACvC,IAAA;AACaC,GAAAA,KAAAA,IAAAA,KAAAA,OAAAA,CAAAA,GAAAA,CAAAA,MAAAA,IAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAAA,CAAAA,IAAAA,MAAAA,OAAAA,CAAAA,CAAAA;;AAErB,kBAAA,eAAA,CAAA;;AAED,EAAA,KAAA,EAAA;AACEC,IAAAA,IAAM,EADuB;AAE7BC,MAAAA,IAAO,EAAA,MAAA;AACLC,MAAAA,QAAM,EAAA,IAAA;AACJH,KAAAA;AACAI,IAAAA,QAAAA,EAAQ,OAAE;;AAEZC,EAAAA,KAAAA,CAAAA,KAAUC,EAAAA;IAPiB,IAAA;;IASxB,WAAQ,YAAA,CAAA,eAAA,CAAA,CAAA;AAAEC,IAAAA,MAAAA;AAAF,MAAU,MAAA;AACrB,MAAA,aAAuB;KACjB,GAAA,MAAA,CAAA,4BAAA,CAAA,CAAA;UAAA;AAAUC,MAAAA,gBAAAA;MAAkBC,gBAAOC;KACnC,GAAA,MAAA,CAAA;UAAA;AAAoBC,MAAAA,IAAAA;AAApB,MAAA,QAAN;KACM,GAAA,KAAA,CAAA;UAAA;AAAQN,MAAAA,IAAAA;AAAR,MAAA,KAAN,EAAA,SAAA;KACM,GAAA,IAAA,CAAA;UAAA,KAAA,GAAA,MAAA;AAAQO,MAAAA,MAAOC,WAAAA,GAAAA,aAAAA,IAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,aAAAA,CAAAA;AAAf,QAA6BV,IAAnC;;OAEMS,CAAAA,CAAAA;MACJ,OAAME,YAAcN,CAAAA,WAAAA,CAAAA,GAAgB,SAAA,GAAA,WAAA,IAAA,IAAA,GAAA,WAAA,GAAA,SAAA,CAAA;;AAAQO,IAAAA,SAAAA,WAAAA,GAAAA;AAAR,MAAA,IAApC,CAAA,gBAAA,IAAA,IAAA,CAAA,MAAA,IAAA,gBAAA,KAAA,CAAA,QAAA,EAAA;QACOjB,IAAAA,CAAAA,mBAAY,EAAA,CAAA,YAAZ,CAAA,CAAA;OAFT;;AAIA,IAAA,OAAA,iBAAuB,CAAA,MAAA,EAAA;MACrB,OACmB,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA;AAGjBS,MAAAA,SAAI,EAAA,WAAA;AACL,KAAA,EAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;AACF,GAAA;;;;;"}