{"version": 3, "file": "node2.mjs", "sources": ["../../../../../../packages/components/cascader-panel/src/node.vue"], "sourcesContent": ["<template>\n  <li\n    :id=\"`${menuId}-${node.uid}`\"\n    role=\"menuitem\"\n    :aria-haspopup=\"!isLeaf\"\n    :aria-owns=\"isLeaf ? undefined : menuId\"\n    :aria-expanded=\"inExpandingPath\"\n    :tabindex=\"expandable ? -1 : undefined\"\n    :class=\"[\n      ns.b(),\n      ns.is('selectable', checkStrictly),\n      ns.is('active', node.checked),\n      ns.is('disabled', !expandable),\n      inExpandingPath && 'in-active-path',\n      inCheckedPath && 'in-checked-path',\n    ]\"\n    @mouseenter=\"handleHoverExpand\"\n    @focus=\"handleHoverExpand\"\n    @click=\"handleClick\"\n  >\n    <!-- prefix -->\n    <el-checkbox\n      v-if=\"multiple && showPrefix\"\n      :model-value=\"node.checked\"\n      :indeterminate=\"node.indeterminate\"\n      :disabled=\"isDisabled\"\n      @click.stop\n      @update:model-value=\"handleSelectCheck\"\n    />\n    <el-radio\n      v-else-if=\"checkStrictly && showPrefix\"\n      :model-value=\"checkedNodeId\"\n      :label=\"node.uid\"\n      :disabled=\"isDisabled\"\n      @update:model-value=\"handleSelectCheck\"\n      @click.stop\n    >\n      <!--\n        Add an empty element to avoid render label,\n        do not use empty fragment here for https://github.com/vuejs/vue-next/pull/2485\n      -->\n      <span />\n    </el-radio>\n    <el-icon v-else-if=\"isLeaf && node.checked\" :class=\"ns.e('prefix')\">\n      <check />\n    </el-icon>\n\n    <!-- content -->\n    <node-content\n      :node=\"node\"\n      :disabled=\"isDisabled\"\n      @handle-select-check=\"handleSelectCheck\"\n    />\n    <!-- postfix -->\n    <template v-if=\"!isLeaf\">\n      <el-icon v-if=\"node.loading\" :class=\"[ns.is('loading'), ns.e('postfix')]\">\n        <loading />\n      </el-icon>\n      <el-icon v-else :class=\"['arrow-right', ns.e('postfix')]\">\n        <arrow-right />\n      </el-icon>\n    </template>\n  </li>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject } from 'vue'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport ElRadio from '@element-plus/components/radio'\nimport ElIcon from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ArrowRight, Check, Loading } from '@element-plus/icons-vue'\nimport NodeContent from './node-content'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\n\nimport type { default as CascaderNode } from './node'\nimport type { PropType } from 'vue'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\n\ndefineOptions({\n  name: 'ElCascaderNode',\n})\n\nconst props = defineProps({\n  node: {\n    type: Object as PropType<CascaderNode>,\n    required: true,\n  },\n  menuId: String,\n})\nconst emit = defineEmits(['expand'])\n\nconst panel = inject(CASCADER_PANEL_INJECTION_KEY)!\n\nconst ns = useNamespace('cascader-node')\nconst isHoverMenu = computed(() => panel.isHoverMenu)\nconst multiple = computed(() => panel.config.multiple)\nconst checkStrictly = computed(() => panel.config.checkStrictly)\nconst showPrefix = computed(() => panel.config.showPrefix)\nconst checkedNodeId = computed(() => panel.checkedNodes[0]?.uid)\nconst isDisabled = computed(() => props.node.isDisabled)\nconst isLeaf = computed(() => props.node.isLeaf)\nconst expandable = computed(\n  () => (checkStrictly.value && !isLeaf.value) || !isDisabled.value\n)\nconst inExpandingPath = computed(() => isInPath(panel.expandingNode!))\n\n// only useful in check-strictly mode\nconst inCheckedPath = computed(\n  () => checkStrictly.value && panel.checkedNodes.some(isInPath)\n)\n\nconst isInPath = (node: CascaderNode) => {\n  const { level, uid } = props.node\n  return node?.pathNodes[level - 1]?.uid === uid\n}\n\nconst doExpand = () => {\n  if (inExpandingPath.value) return\n  panel.expandNode(props.node)\n}\n\nconst doCheck = (checked: boolean) => {\n  const { node } = props\n  if (checked === node.checked) return\n  panel.handleCheckChange(node, checked)\n}\n\nconst doLoad = () => {\n  panel.lazyLoad(props.node, () => {\n    if (!isLeaf.value) doExpand()\n  })\n}\n\nconst handleHoverExpand = (e: Event) => {\n  if (!isHoverMenu.value) return\n  handleExpand()\n  !isLeaf.value && emit('expand', e)\n}\n\nconst handleExpand = () => {\n  const { node } = props\n  // do not exclude leaf node because the menus expanded might have to reset\n  if (!expandable.value || node.loading) return\n  node.loaded ? doExpand() : doLoad()\n}\n\nconst handleClick = () => {\n  if (isHoverMenu.value && !isLeaf.value) return\n\n  if (\n    isLeaf.value &&\n    !isDisabled.value &&\n    !checkStrictly.value &&\n    !multiple.value\n  ) {\n    handleCheck(true)\n  } else {\n    handleExpand()\n  }\n}\n\nconst handleSelectCheck = (checked: CheckboxValueType | undefined) => {\n  if (checkStrictly.value) {\n    doCheck(checked as boolean)\n    if (props.node.loaded) {\n      doExpand()\n    }\n  } else {\n    handleCheck(checked as boolean)\n  }\n}\n\nconst handleCheck = (checked: boolean) => {\n  if (!props.node.loaded) {\n    doLoad()\n  } else {\n    doCheck(checked)\n    !checkStrictly.value && doExpand()\n  }\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;mCA+Ec,CAAA;AAAA,EACZ,IAAM,EAAA,gBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;AAWA,IAAM,MAAA,KAAA,GAAQ,OAAO,4BAA4B,CAAA,CAAA;AAEjD,IAAM,MAAA,EAAA,GAAK,aAAa,eAAe,CAAA,CAAA;AACvC,IAAA,MAAM,WAAc,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,WAAW,CAAA,CAAA;AACpD,IAAA,MAAM,QAAW,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,OAAO,QAAQ,CAAA,CAAA;AACrD,IAAA,MAAM,aAAgB,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,OAAO,aAAa,CAAA,CAAA;AAC/D,IAAA,MAAM,UAAa,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,OAAO,UAAU,CAAA,CAAA;AACzD,IAAA,MAAM,gBAAgB,QAAS,CAAA,MAAM;AACrC,MAAA,IAAM,EAAa,CAAA;AACnB,MAAA,OAAe,CAAA,EAAA,GAAA,KAAA,CAAA,YAAe,CAAA,CAAA,CAAM,KAAK,IAAM,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA;AAC/C,KAAA,CAAA,CAAA;AAAmB,IAAA,gBACI,GAAA,QAAA,CAAA,MAAiB,KAAA,CAAA,IAAA,CAAA,UAAsB,CAAA,CAAA;AAAA,IAC9D,MAAA,MAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;AACA,IAAA,MAAM,qBAA2B,CAAA,MAAA,aAAe,CAAA,KAAA,iBAAqB,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAGrE,IAAA,MAAM,eAAgB,GAAA,QAAA,CAAA,MAAA,QAAA,CAAA,KAAA,CAAA,aAAA,CAAA,CAAA,CAAA;AAAA,IAAA,mBACA,GAAA,QAAA,CAAS,MAAM,aAAa,MAAa,IAAA,KAAA,CAAA,YAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,IAC/D,MAAA,QAAA,GAAA,CAAA,IAAA,KAAA;AAEA,MAAM,IAAA,EAAA,CAAA;AACJ,MAAA,MAAM,EAAE,KAAA,EAAO,GAAI,EAAA,GAAI,KAAM,CAAA,IAAA,CAAA;AAC7B,MAAA,OAAO,CAAM,CAAA,EAAA,GAAA,IAAA,IAAA,IAAkB,GAAA,MAAI,GAAQ,IAAA,CAAA,SAAA,CAAA,KAAA,GAAA,CAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,MAAA,GAAA,CAAA;AAAA,KAC7C,CAAA;AAEA,IAAA,MAAM,WAAW,MAAM;AACrB,MAAA,IAAI,gBAAgB,KAAO;AAC3B,QAAM,OAAA;AAAqB,MAC7B,KAAA,CAAA,UAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAM,MAAA,UAAW,CAAA,OAAA,KAAA;AACjB,MAAI,MAAA,EAAA,IAAA,EAAA,QAA0B,CAAA;AAC9B,MAAM,IAAA,OAAA,KAAA,IAAA,CAAA;AAA+B,QACvC,OAAA;AAEA,MAAA,uBAAqB,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;AACnB,KAAM,CAAA;AACJ,IAAI,MAAA,MAAQ,GAAA,MAAA;AAAgB,MAC9B,KAAC,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA,MAAA;AAAA,QACH,IAAA,CAAA,MAAA,CAAA,KAAA;AAEA,UAAM,QAAA,EAAA,CAAA;AACJ,OAAI,CAAA,CAAA;AACJ,KAAa,CAAA;AACb,IAAA,MAAQ,iBAAc,GAAA,CAAA,CAAA,KAAA;AAAW,MACnC,IAAA,CAAA,WAAA,CAAA,KAAA;AAEA,QAAA;AACE,MAAM,cAAW,CAAA;AAEjB,MAAA,CAAA,MAAK,CAAA,KAAA,IAAoB,IAAA,CAAA,QAAK,EAAS,CAAA,CAAA,CAAA;AACvC,KAAK,CAAA;AAA6B,IACpC,MAAA,YAAA,GAAA,MAAA;AAEA,MAAA,sBAA0B,CAAA;AACxB,MAAA,IAAI,CAAY,UAAA,CAAA,KAAA,IAAS,IAAC,CAAA,OAAc;AAExC,QACE,OAAA;AAKA,MAAA,IAAA,CAAA,MAAA,GAAY,QAAI,EAAA,GAAA,MAAA,EAAA,CAAA;AAAA,KAAA,CAClB;AACE,IAAa,MAAA,WAAA,GAAA,MAAA;AAAA,MACf,IAAA,WAAA,CAAA,KAAA,IAAA,CAAA,MAAA,CAAA,KAAA;AAAA,QACF,OAAA;AAEA,MAAM,IAAA,MAAA,CAAA,KAAA,IAAA,CAAA,UAAgE,CAAA,KAAA,IAAA,CAAA,aAAA,CAAA,KAAA,IAAA,CAAA,QAAA,CAAA,KAAA,EAAA;AACpE,QAAA,gBAAkB,CAAO,CAAA;AACvB,OAAA,MAAA;AACA,QAAI,eAAW;AACb,OAAS;AAAA,KACX,CAAA;AAAA,IAAA,MACK,iBAAA,GAAA,CAAA,OAAA,KAAA;AACL,MAAA,IAAA,aAA8B,CAAA,KAAA,EAAA;AAAA,QAChC,OAAA,CAAA,OAAA,CAAA,CAAA;AAAA,QACF,IAAA,KAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AAEA,UAAM,QAAA,EAAA,CAAA;AACJ,SAAI;AACF,OAAO,MAAA;AAAA,QACF,WAAA,CAAA,OAAA,CAAA,CAAA;AACL,OAAA;AACA,KAAC,CAAA;AAAgC,IACnC,MAAA,WAAA,GAAA,CAAA,OAAA,KAAA;AAAA,MACF,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,MAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}